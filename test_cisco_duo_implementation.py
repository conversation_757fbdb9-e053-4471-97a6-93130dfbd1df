#!/usr/bin/env python3
"""
Simple test script to validate Cisco Duo implementation
"""

import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_authentication_mapping():
    """Test the authentication log normalization"""
    try:
        from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.authentication import (
            map_factor_type,
            map_status_and_disposition,
            normalize_authentication_log
        )
        from apps.connectors.integrations.schemas import ocsf
        
        print("✓ Successfully imported authentication functions")
        
        # Test factor type mapping
        test_factors = [
            "duo_push",
            "hardware_token", 
            "sms_passcode",
            "webauthn_credential",
            "not_available"
        ]
        
        print("\nTesting factor type mappings:")
        for factor in test_factors:
            result = map_factor_type(factor)
            print(f"  {factor} -> {result}")
        
        # Test status and disposition mapping
        test_cases = [
            ("success", "user_approved"),
            ("denied", "invalid_passcode"),
            ("denied", "location_restricted"),
            ("error", "system_error")
        ]
        
        print("\nTesting status and disposition mappings:")
        for result, reason in test_cases:
            status, disposition = map_status_and_disposition(result, reason)
            print(f"  {result}/{reason} -> {status}/{disposition}")
        
        # Test with sample authentication log
        sample_log = {
            "access_device": {
                "browser": "Chrome",
                "browser_version": "91.0.4472.124",
                "ip": "*************",
                "hostname": "test-workstation",
                "os": "Windows",
                "os_version": "10",
                "location": {
                    "city": "San Francisco",
                    "country": "United States",
                    "state": "California",
                    "latitude": 37.7749,
                    "longitude": -122.4194
                }
            },
            "user": {
                "name": "Test User",
                "key": "DAAAAAAAAAAAAAAAAAA1",
                "groups": ["Test Group"]
            },
            "application": {
                "name": "Test Application",
                "key": "DIAAAAAAAAAAAAAAAAA1"
            },
            "factor": "duo_push",
            "result": "success",
            "reason": "user_approved",
            "event_type": "authentication",
            "timestamp": 1672574400,
            "isotimestamp": "2023-01-01T12:00:00+00:00",
            "txid": "12345678-1234-1234-1234-123456789abc",
            "email": "<EMAIL>"
        }
        
        print("\nTesting authentication log normalization:")
        normalized = normalize_authentication_log(sample_log)
        print(f"  Activity: {normalized.activity}")
        print(f"  Status: {normalized.status}")
        print(f"  Disposition: {normalized.disposition}")
        print(f"  User: {normalized.actor.user.name}")
        print(f"  Factor type: {normalized.auth_factors[0].factor_type if normalized.auth_factors else 'None'}")
        
        print("\n✓ All authentication tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Authentication test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_action_imports():
    """Test importing the action classes"""
    try:
        from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions import (
            CiscoDuoV1DisableUser,
            CiscoDuoV1LockUser,
            CiscoDuoV1UnlockUser,
            CiscoDuoV1GetSignInLogsByUser
        )
        print("✓ Successfully imported all action classes")
        return True
    except Exception as e:
        print(f"✗ Action import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Testing Cisco Duo Implementation")
    print("=" * 40)
    
    tests = [
        test_action_imports,
        test_authentication_mapping,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
