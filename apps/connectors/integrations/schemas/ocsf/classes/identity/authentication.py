from typing import ClassVar, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.enums import (
    AuthenticationActivity,
    IdentityAndAccessManagement,
)
from apps.connectors.integrations.schemas.ocsf.objects import (
    AuthenticationFactor,
    NetworkEndpoint,
    Service,
)

from .base_identity import BaseIdentity


class Authentication(BaseIdentity):
    ocsf_class: ClassVar = IdentityAndAccessManagement.AUTHENTICATION
    activity_enum: ClassVar = AuthenticationActivity

    auth_factors: Optional[list[AuthenticationFactor]] = Field(
        default=None,
        title="Authentication Factors",
        description="The authentication factors used in the authentication attempt.",
    )
    dst_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Destination Endpoint",
        description="The endpoint to which the authentication was targeted.",
    )
    service: Optional[Service] = Field(
        default=None,
        title="Service",
        description="The service or gateway to which the user or process is being authenticated.",
    )
    src_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Source Endpoint",
        description="Details about the source of the IAM activity.",
    )
